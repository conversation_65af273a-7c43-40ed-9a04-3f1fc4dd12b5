/**
 * Advanced Field Matching Algorithms
 *
 * Sophisticated matching algorithms for custom field synchronization
 * between AutoPatient and CliniCore platforms. Provides intelligent
 * field matching with multiple strategies and comprehensive result tracking.
 *
 * @fileoverview Advanced field matching algorithms and utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { getRequestId } from "@/utils/getRequestId";
import { logDebug, logInfo, logWarn } from "@/utils/logger";
import { areFieldTypesCompatible } from "../conflict/compatibility";
import {
	normalizeFieldName,
	normalizedFieldsMatch,
	areFieldNameVariations,
	calculateFieldNameSimilarity
} from "../config/fieldNormalization";
import {
	findMatchingTextboxListField,
	areTextboxListFieldsCompatible
} from "../config/textboxListHandling";
import type { Platform, FieldMatchResult, CustomFieldMapping } from "../types";

/**
 * Match AP field with CC fields using comprehensive strategies
 *
 * Attempts to find the best matching CC field for a given AP field using
 * intelligent name matching and type compatibility checking. Uses multiple
 * matching strategies in order of preference.
 *
 * @param apField - AutoPatient custom field to match
 * @param ccFields - Array of CliniCore custom fields to match against
 * @param existingMappings - Existing field mappings to avoid conflicts
 * @param requestId - Request ID for tracing (optional, will generate if not provided)
 * @returns Field match result with details
 *
 * @example
 * ```typescript
 * const matchResult = matchAPFieldWithCC(
 *   apField,
 *   ccCustomFields,
 *   existingMappings,
 *   "req-123"
 * );
 *
 * if (matchResult.matched) {
 *   console.log(`Matched: ${apField.name} -> ${matchResult.ccField?.name}`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export function matchAPFieldWithCC(
	apField: APGetCustomFieldType,
	ccFields: GetCCCustomField[],
	existingMappings: CustomFieldMapping[],
	requestId?: string,
): FieldMatchResult {
	const traceId = requestId || getRequestId();
	
	logDebug("Matching AP field with CC fields", {
		requestId: traceId,
		apFieldId: apField.id,
		apFieldName: apField.name,
		apFieldType: apField.dataType,
		ccFieldCount: ccFields.length,
	});

	// Filter out CC fields that are already mapped
	const mappedCcIds = new Set(
		existingMappings
			.filter(mapping => mapping.ccId !== null)
			.map(mapping => mapping.ccId)
	);

	const availableCCFields = ccFields.filter(ccField => 
		!mappedCcIds.has(ccField.id)
	);

	if (availableCCFields.length === 0) {
		logDebug("No available CC fields for matching", {
			requestId: traceId,
			apFieldId: apField.id,
			totalCcFields: ccFields.length,
			mappedCcFields: mappedCcIds.size,
		});
		return {
			matched: false,
			apField,
			matchType: "none",
			conflictReason: "No available CC fields",
		};
	}

	// Try exact name match first
	const exactMatch = findExactNameMatch(apField, availableCCFields);
	if (exactMatch) {
		const isCompatible = areFieldTypesCompatible(
			apField,
			exactMatch,
			"ap",
		);

		if (isCompatible) {
			logInfo("Found exact name match with compatible types", {
				requestId: traceId,
				apFieldId: apField.id,
				ccFieldId: exactMatch.id,
				apFieldName: apField.name,
				ccFieldName: exactMatch.name,
			});
			return {
				matched: true,
				apField,
				ccField: exactMatch,
				matchType: "exact",
			};
		} else {
			logWarn("Exact name match found but types incompatible", {
				requestId: traceId,
				apFieldId: apField.id,
				apFieldName: apField.name,
				ccFieldId: exactMatch.id,
				ccFieldName: exactMatch.name,
				apFieldType: apField.dataType,
				ccFieldType: exactMatch.type,
				ccAllowMultiple: exactMatch.allowMultipleValues,
			});
		}
	}

	// Try normalized name match
	const normalizedMatch = findNormalizedNameMatch(apField, availableCCFields);
	if (normalizedMatch) {
		const isCompatible = areFieldTypesCompatible(
			apField,
			normalizedMatch,
			"ap",
		);

		if (isCompatible) {
			logInfo("Found normalized name match with compatible types", {
				requestId: traceId,
				apFieldId: apField.id,
				ccFieldId: normalizedMatch.id,
				apFieldName: apField.name,
				ccFieldName: normalizedMatch.name,
			});
			return {
				matched: true,
				apField,
				ccField: normalizedMatch,
				matchType: "normalized",
			};
		}
	}

	// Try similarity-based matching
	const similarityMatch = findSimilarityMatch(apField, availableCCFields, 0.8);
	if (similarityMatch) {
		const isCompatible = areFieldTypesCompatible(
			apField,
			similarityMatch.field,
			"ap",
		);

		if (isCompatible) {
			logInfo("Found similarity-based match with compatible types", {
				requestId: traceId,
				apFieldId: apField.id,
				ccFieldId: similarityMatch.field.id,
				apFieldName: apField.name,
				ccFieldName: similarityMatch.field.name,
				similarity: similarityMatch.similarity,
			});
			return {
				matched: true,
				apField,
				ccField: similarityMatch.field,
				matchType: "normalized",
			};
		}
	}

	logDebug("No compatible match found for AP field", {
		requestId: traceId,
		apFieldId: apField.id,
		apFieldName: apField.name,
		apFieldType: apField.dataType,
		availableFieldCount: availableCCFields.length,
	});

	return {
		matched: false,
		apField,
		matchType: "none",
		conflictReason: "No compatible match found",
	};
}

/**
 * Match CC field with AP fields using comprehensive strategies
 *
 * Attempts to find the best matching AP field for a given CC field.
 * Similar to AP-to-CC matching but optimized for CC field characteristics.
 *
 * @param ccField - CliniCore custom field to match
 * @param apFields - Array of AutoPatient custom fields to match against
 * @param existingMappings - Existing field mappings to avoid conflicts
 * @param requestId - Request ID for tracing (optional, will generate if not provided)
 * @returns Field match result with details
 *
 * @example
 * ```typescript
 * const matchResult = matchCCFieldWithAP(
 *   ccField,
 *   apCustomFields,
 *   existingMappings,
 *   "req-123"
 * );
 *
 * if (matchResult.matched) {
 *   console.log(`Matched: ${ccField.name} -> ${matchResult.apField?.name}`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export function matchCCFieldWithAP(
	ccField: GetCCCustomField,
	apFields: APGetCustomFieldType[],
	existingMappings: CustomFieldMapping[],
	requestId?: string,
): FieldMatchResult {
	const traceId = requestId || getRequestId();
	
	logDebug("Matching CC field with AP fields", {
		requestId: traceId,
		ccFieldId: ccField.id,
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		apFieldCount: apFields.length,
	});

	// Filter out AP fields that are already mapped
	const mappedApIds = new Set(
		existingMappings
			.filter(mapping => mapping.apId !== null)
			.map(mapping => mapping.apId)
	);

	const availableAPFields = apFields.filter(apField => 
		!mappedApIds.has(apField.id)
	);

	if (availableAPFields.length === 0) {
		logDebug("No available AP fields for matching", {
			requestId: traceId,
			ccFieldId: ccField.id,
			totalApFields: apFields.length,
			mappedApFields: mappedApIds.size,
		});
		return {
			matched: false,
			ccField,
			matchType: "none",
			conflictReason: "No available AP fields",
		};
	}

	// Try exact name match first
	const exactMatch = findExactNameMatchForCC(ccField, availableAPFields);
	if (exactMatch) {
		const isCompatible = areFieldTypesCompatible(
			ccField,
			exactMatch,
			"cc",
		);

		if (isCompatible) {
			logInfo("Found exact name match with compatible types", {
				requestId: traceId,
				ccFieldId: ccField.id,
				apFieldId: exactMatch.id,
				ccFieldName: ccField.name,
				apFieldName: exactMatch.name,
			});
			return {
				matched: true,
				apField: exactMatch,
				ccField,
				matchType: "exact",
			};
		}
	}

	// Try normalized name match
	const normalizedMatch = findNormalizedNameMatchForCC(ccField, availableAPFields);
	if (normalizedMatch) {
		const isCompatible = areFieldTypesCompatible(
			ccField,
			normalizedMatch,
			"cc",
		);

		if (isCompatible) {
			logInfo("Found normalized name match with compatible types", {
				requestId: traceId,
				ccFieldId: ccField.id,
				apFieldId: normalizedMatch.id,
				ccFieldName: ccField.name,
				apFieldName: normalizedMatch.name,
			});
			return {
				matched: true,
				apField: normalizedMatch,
				ccField,
				matchType: "normalized",
			};
		}
	}

	logDebug("No compatible match found for CC field", {
		requestId: traceId,
		ccFieldId: ccField.id,
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		availableFieldCount: availableAPFields.length,
	});

	return {
		matched: false,
		ccField,
		matchType: "none",
		conflictReason: "No compatible match found",
	};
}

/**
 * Find exact name match for AP field
 */
function findExactNameMatch(
	apField: APGetCustomFieldType,
	ccFields: GetCCCustomField[],
): GetCCCustomField | undefined {
	return ccFields.find(ccField =>
		ccField.name === apField.name ||
		ccField.label === apField.name ||
		(apField.fieldKey && (ccField.name === apField.fieldKey || ccField.label === apField.fieldKey))
	);
}

/**
 * Find exact name match for CC field
 */
function findExactNameMatchForCC(
	ccField: GetCCCustomField,
	apFields: APGetCustomFieldType[],
): APGetCustomFieldType | undefined {
	return apFields.find(apField =>
		apField.name === ccField.name ||
		apField.name === ccField.label ||
		(apField.fieldKey && (apField.fieldKey === ccField.name || apField.fieldKey === ccField.label))
	);
}

/**
 * Find normalized name match for AP field
 */
function findNormalizedNameMatch(
	apField: APGetCustomFieldType,
	ccFields: GetCCCustomField[],
): GetCCCustomField | undefined {
	const normalizedApName = normalizeFieldName(apField.name);
	const normalizedApFieldKey = apField.fieldKey ? normalizeFieldName(apField.fieldKey) : null;

	return ccFields.find(ccField => {
		const normalizedCcName = normalizeFieldName(ccField.name);
		const normalizedCcLabel = normalizeFieldName(ccField.label);

		return normalizedApName === normalizedCcName ||
			   normalizedApName === normalizedCcLabel ||
			   (normalizedApFieldKey && (
				   normalizedApFieldKey === normalizedCcName ||
				   normalizedApFieldKey === normalizedCcLabel
			   ));
	});
}

/**
 * Find normalized name match for CC field
 */
function findNormalizedNameMatchForCC(
	ccField: GetCCCustomField,
	apFields: APGetCustomFieldType[],
): APGetCustomFieldType | undefined {
	const normalizedCcName = normalizeFieldName(ccField.name);
	const normalizedCcLabel = normalizeFieldName(ccField.label);

	return apFields.find(apField => {
		const normalizedApName = normalizeFieldName(apField.name);
		const normalizedApFieldKey = apField.fieldKey ? normalizeFieldName(apField.fieldKey) : null;

		return normalizedCcName === normalizedApName ||
			   normalizedCcLabel === normalizedApName ||
			   (normalizedApFieldKey && (
				   normalizedCcName === normalizedApFieldKey ||
				   normalizedCcLabel === normalizedApFieldKey
			   ));
	});
}

/**
 * Find similarity-based match for AP field
 */
function findSimilarityMatch(
	apField: APGetCustomFieldType,
	ccFields: GetCCCustomField[],
	minSimilarity: number,
): { field: GetCCCustomField; similarity: number } | undefined {
	let bestMatch: { field: GetCCCustomField; similarity: number } | undefined;

	for (const ccField of ccFields) {
		const nameSimilarity = calculateFieldNameSimilarity(apField.name, ccField.name);
		const labelSimilarity = calculateFieldNameSimilarity(apField.name, ccField.label);
		const maxSimilarity = Math.max(nameSimilarity, labelSimilarity);

		if (maxSimilarity >= minSimilarity) {
			if (!bestMatch || maxSimilarity > bestMatch.similarity) {
				bestMatch = { field: ccField, similarity: maxSimilarity };
			}
		}
	}

	return bestMatch;
}
