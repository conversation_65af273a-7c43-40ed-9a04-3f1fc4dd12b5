/**
 * Field Type Compatibility Utilities
 *
 * Provides utilities for checking field type compatibility between
 * AutoPatient and CliniCore platforms. Determines whether fields
 * with different types can be safely mapped together for value
 * synchronization.
 *
 * @fileoverview Field type compatibility checking utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { findExistingCustomField } from "../matching/strategies";
import type { Platform } from "../types";

/**
 * Check if two field types are compatible for mapping
 *
 * Determines whether fields with different types can be safely mapped together
 * for value synchronization. Compatible types allow direct value conversion,
 * while incompatible types would require fallback conversions.
 *
 * @param sourceField - Source field to check compatibility for
 * @param targetField - Target field to check compatibility against
 * @param sourcePlatform - Platform of the source field ("ap" | "cc")
 * @returns True if field types are compatible for mapping, false otherwise
 *
 * @example
 * ```typescript
 * const ccSelectField = { type: "select", allowMultipleValues: false };
 * const apRadioField = { dataType: "RADIO" };
 * const apTextField = { dataType: "TEXT" };
 *
 * console.log(areFieldTypesCompatible(ccSelectField, apRadioField, "cc")); // true
 * console.log(areFieldTypesCompatible(ccSelectField, apTextField, "cc")); // false
 * ```
 *
 * @since 2.0.0
 */
export function areFieldTypesCompatible(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetField: APGetCustomFieldType | GetCCCustomField,
	sourcePlatform: Platform,
): boolean {
	if (sourcePlatform === "cc") {
		const ccField = sourceField as GetCCCustomField;
		const apField = targetField as APGetCustomFieldType;
		return isCcToApTypeCompatible(ccField, apField);
	} else {
		const apField = sourceField as APGetCustomFieldType;
		const ccField = targetField as GetCCCustomField;
		return isApToCcTypeCompatible(apField, ccField);
	}
}

/**
 * Check if CC field type is compatible with AP field type
 *
 * @param ccField - CliniCore field to check
 * @param apField - AutoPatient field to check against
 * @returns True if types are compatible
 */
function isCcToApTypeCompatible(
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
): boolean {
	const ccType = ccField.type;
	const apType = apField.dataType;
	const allowMultiple = ccField.allowMultipleValues;

	// Core logic: CC fields with allowMultipleValues=true become TEXTBOX_LIST in AP
	const compatibilityMap: Record<string, string[]> = {
		text: allowMultiple
			? ["TEXTBOX_LIST"] // Multi-value text ALWAYS becomes TEXTBOX_LIST
			: ["TEXT", "LARGE_TEXT"],
		textarea: allowMultiple
			? ["TEXTBOX_LIST"] // Multi-value textarea ALWAYS becomes TEXTBOX_LIST
			: ["LARGE_TEXT", "TEXT"],
		number: allowMultiple
			? ["TEXTBOX_LIST"] // Multi-value number ALWAYS becomes TEXTBOX_LIST
			: ["NUMERICAL", "MONETORY"],
		telephone: allowMultiple
			? ["TEXTBOX_LIST"] // Multi-value telephone ALWAYS becomes TEXTBOX_LIST
			: ["PHONE", "TEXT"],
		email: allowMultiple
			? ["TEXTBOX_LIST"] // Multi-value email ALWAYS becomes TEXTBOX_LIST
			: ["EMAIL", "TEXT"],
		date: allowMultiple
			? ["TEXTBOX_LIST"] // Multi-value date ALWAYS becomes TEXTBOX_LIST
			: ["DATE"],
		boolean: ["RADIO"], // Only for Yes/No radio fields
		select: allowMultiple
			? ["MULTIPLE_OPTIONS", "CHECKBOX"]
			: ["SINGLE_OPTIONS", "RADIO"],
		"select-or-custom": allowMultiple
			? ["MULTIPLE_OPTIONS", "CHECKBOX"]
			: ["SINGLE_OPTIONS", "RADIO"],
	};

	const compatibleTypes = compatibilityMap[ccType];

	// If we have a direct mapping, use it
	if (compatibleTypes) {
		return compatibleTypes.includes(apType);
	}

	// For unmapped CC field types, they all map to TEXT via fallback conversion
	// So they should be compatible with TEXT and LARGE_TEXT AP fields
	if (apType === "TEXT" || apType === "LARGE_TEXT") {
		return true; // Any unmapped CC field type maps to TEXT via fallback
	}

	return false;
}

/**
 * Check if AP field type is compatible with CC field type
 *
 * @param apField - AutoPatient field to check
 * @param ccField - CliniCore field to check against
 * @returns True if types are compatible
 */
function isApToCcTypeCompatible(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
): boolean {
	const apType = apField.dataType;
	const ccType = ccField.type;
	const allowMultiple = ccField.allowMultipleValues;

	// Core standard type mappings only - no hard-coded field names
	const compatibilityMap: Record<string, string[]> = {
		TEXT: ["text", "textarea"],
		LARGE_TEXT: ["textarea", "text"],
		NUMERICAL: ["number", "text"],
		PHONE: ["telephone", "text"],
		MONETORY: ["number", "text"],
		EMAIL: ["email", "text"],
		DATE: ["date", "text"],
		CHECKBOX: ["select", "select-or-custom"],
		SINGLE_OPTIONS: ["select", "select-or-custom"],
		MULTIPLE_OPTIONS: ["select", "select-or-custom"],
		RADIO: ["select", "select-or-custom", "boolean"],
		TEXTBOX_LIST: allowMultiple
			? ["text", "textarea", "number", "telephone", "email", "date"] // ONLY these 6 types with allowMultiple=true
			: [],
		FILE_UPLOAD: [], // No direct CC equivalent
	};

	const compatibleTypes = compatibilityMap[apType] || [];

	// If we have a direct mapping, use it
	if (compatibleTypes.includes(ccType)) {
		return true;
	}

	// For AP TEXT/LARGE_TEXT fields, they should be compatible with any unknown CC field type
	// since unknown CC types convert to text via fallback conversion
	if (apType === "TEXT" || apType === "LARGE_TEXT") {
		// Check if this is a known core CC type that has specific mappings
		const knownCcTypes = ["text", "textarea", "number", "telephone", "email", "date", "boolean", "select", "select-or-custom"];

		// If it's an unknown CC type, it's compatible with TEXT/LARGE_TEXT
		if (!knownCcTypes.includes(ccType)) {
			return true;
		}
	}

	return false;
}

/**
 * Generate unique field name to avoid conflicts
 *
 * Creates a unique field name by appending type and counter suffixes
 * to avoid conflicts with existing fields. This is used when field
 * creation fails due to name conflicts.
 *
 * @param originalName - Original field name that caused conflict
 * @param sourceField - Source field causing the conflict
 * @param sourcePlatform - Platform of the source field
 * @param existingFields - Array of existing fields to check against
 * @returns Unique field name that doesn't conflict with existing fields
 *
 * @example
 * ```typescript
 * const uniqueName = generateUniqueFieldName(
 *   "preferences",
 *   apField,
 *   "ap",
 *   existingCcFields
 * );
 * // Result: "preferences_select" or "preferences_select_2" if needed
 * ```
 *
 * @since 2.0.0
 */
export function generateUniqueFieldName(
	originalName: string,
	sourceField: APGetCustomFieldType | GetCCCustomField,
	sourcePlatform: Platform,
	existingFields: (APGetCustomFieldType | GetCCCustomField)[],
): string {
	// Generate type suffix based on field type
	let typeSuffix: string;

	if (sourcePlatform === "cc") {
		const ccField = sourceField as GetCCCustomField;
		switch (ccField.type) {
			case "select":
				typeSuffix = ccField.allowMultipleValues ? "multiselect" : "select";
				break;
			case "boolean":
				typeSuffix = "boolean";
				break;
			case "select-or-custom":
				typeSuffix = "selectcustom";
				break;
			default:
				typeSuffix = ccField.type;
		}
	} else {
		const apField = sourceField as APGetCustomFieldType;
		switch (apField.dataType) {
			case "RADIO":
				typeSuffix = "radio";
				break;
			case "MULTIPLE_OPTIONS":
				typeSuffix = "multiselect";
				break;
			case "SINGLE_OPTIONS":
				typeSuffix = "singleselect";
				break;
			default:
				typeSuffix = apField.dataType.toLowerCase();
		}
	}

	// Generate base unique name
	let uniqueName = `${originalName}_${typeSuffix}`;
	let counter = 1;

	// Check if the generated name conflicts with existing fields
	while (findExistingCustomField(uniqueName, existingFields, sourcePlatform === "cc" ? "ap" : "cc")) {
		counter++;
		uniqueName = `${originalName}_${typeSuffix}_${counter}`;
	}

	return uniqueName;
}
