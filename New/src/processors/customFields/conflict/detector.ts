/**
 * Custom Fields Conflict Detection
 *
 * Provides comprehensive conflict detection for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Detects standard
 * field conflicts, existing custom field conflicts, and blocklist violations
 * to prevent duplicate field creation and maintain data integrity.
 *
 * @fileoverview Conflict detection utilities for custom field synchronization
 * @version 2.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import {
	checkApToCcCreationBlocklist,
	findStandardFieldMapping,
	isStandardFieldConflict,
	type StandardFieldMapping,
} from "@/config/standardFieldMappings";
import { logInfo } from "@/utils/logger";
import { getRequestId } from "@/utils/getRequestId";
import { findExistingCustomField } from "../matching/strategies";
import type { FieldConflictResult, Platform } from "../types";

/**
 * Check if a field name conflicts with standard fields on the target platform
 *
 * This function prevents custom field creation when the field name matches
 * a standard field on the target platform. It uses the comprehensive standard
 * field mappings to identify conflicts and suggest appropriate mappings.
 *
 * When a conflict is detected, the function logs detailed information about
 * the standard field mapping and returns the mapping configuration for
 * further processing.
 *
 * @param fieldName - Name of the field to check for standard field conflicts
 * @param sourcePlatform - Platform where the field originates ("ap" | "cc")
 * @param targetPlatform - Platform where the field would be created ("ap" | "cc")
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Standard field mapping if conflict found, null if field can be created safely
 *
 * @example
 * ```typescript
 * const mapping = checkForStandardFieldMapping(
 *   "email",
 *   "ap",
 *   "cc",
 *   "req-123"
 * );
 *
 * if (mapping) {
 *   console.log(`Field maps to standard field: ${mapping.targetField}`);
 *   // Create mapping to standard field instead of custom field
 * } else {
 *   // Safe to create custom field
 * }
 * ```
 *
 * @since 2.0.0
 */
export function checkForStandardFieldMapping(
	fieldName: string,
	sourcePlatform: Platform,
	targetPlatform: Platform,
	requestId?: string,
): StandardFieldMapping | null {
	const traceId = requestId || getRequestId();
	
	// Check if field name conflicts with standard fields
	if (isStandardFieldConflict(fieldName, targetPlatform)) {
		const standardMapping = findStandardFieldMapping(
			fieldName,
			sourcePlatform,
			targetPlatform,
		);

		if (standardMapping) {
			logInfo("Found standard field mapping for custom field", {
				requestId: traceId,
				fieldName,
				sourcePlatform,
				targetPlatform,
				standardField: standardMapping.targetField,
				action: "creating_standard_field_mapping",
			});
			return standardMapping;
		}

		logInfo("Field conflicts with standard field but no mapping found", {
			requestId: traceId,
			fieldName,
			sourcePlatform,
			targetPlatform,
			action: "skipping_field_creation",
		});
	}

	return null;
}

/**
 * Check if a field conflicts with existing custom fields on the target platform
 *
 * This function prevents duplicate custom field creation by detecting when
 * a field name already exists on the target platform as a custom field.
 * It uses comprehensive field matching to identify potential conflicts.
 *
 * When a conflict is detected, the function logs detailed information about
 * the existing field and recommends creating a mapping instead of a duplicate.
 *
 * @param sourceField - Source custom field to check for conflicts
 * @param targetPlatformFields - Existing custom fields on target platform
 * @param targetPlatform - Target platform identifier ("ap" | "cc")
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Existing field if conflict found, null if field can be created safely
 *
 * @example
 * ```typescript
 * const existingField = checkForExistingCustomFieldConflict(
 *   apField,
 *   ccCustomFields,
 *   "cc",
 *   "req-123"
 * );
 *
 * if (existingField) {
 *   console.log(`Conflict with existing field: ${existingField.name}`);
 *   // Create mapping to existing field instead of duplicate
 * } else {
 *   // Safe to create new custom field
 * }
 * ```
 *
 * @since 2.0.0
 */
export function checkForExistingCustomFieldConflict(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetPlatformFields: (APGetCustomFieldType | GetCCCustomField)[],
	targetPlatform: Platform,
	requestId?: string,
): APGetCustomFieldType | GetCCCustomField | null {
	const traceId = requestId || getRequestId();

	// Determine the actual field name that would be used for creation
	// This matches the logic used in field conversion
	let fieldNameToCheck: string;

	if (targetPlatform === "ap" && "label" in sourceField) {
		// For CC->AP conversion, use label if available, then name
		const ccField = sourceField as GetCCCustomField;
		fieldNameToCheck = ccField.label || ccField.name;
	} else {
		// For AP->CC conversion or when no label, use name
		fieldNameToCheck = sourceField.name;
	}

	const existingField = findExistingCustomField(
		fieldNameToCheck,
		targetPlatformFields,
		targetPlatform,
	);

	if (existingField) {
		logInfo("Found existing custom field conflict", {
			requestId: traceId,
			sourceFieldId: sourceField.id,
			sourceFieldName: sourceField.name,
			fieldNameToCheck,
			existingFieldId: existingField.id,
			existingFieldName: existingField.name,
			targetPlatform,
			action: "creating_mapping_instead_of_duplicate",
		});
		return existingField;
	}

	return null;
}

/**
 * Check if an AP field is blocked from creation in CC
 *
 * This function checks if an AutoPatient field is on the blocklist for
 * creation in CliniCore. The blocklist prevents creation of fields that
 * are known to cause issues or conflicts in the target platform.
 *
 * When a blocklist violation is detected, the function logs detailed
 * information about the blocked field and the reason for blocking.
 *
 * @param apField - AutoPatient field to check against blocklist
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Blocklist entry if field is blocked, null if field can be created
 *
 * @example
 * ```typescript
 * const blocklistEntry = checkApFieldCreationBlocklist(apField, "req-123");
 *
 * if (blocklistEntry) {
 *   console.log(`Field blocked: ${blocklistEntry.reason}`);
 *   // Skip field creation
 * } else {
 *   // Safe to create field
 * }
 * ```
 *
 * @since 2.0.0
 */
export function checkApFieldCreationBlocklist(
	apField: APGetCustomFieldType,
	requestId?: string,
): { pattern: string; reason: string; isRegex: boolean } | null {
	const traceId = requestId || getRequestId();
	const blocklistEntry = checkApToCcCreationBlocklist(apField.name);

	if (blocklistEntry) {
		logInfo("AP field blocked from CC creation", {
			requestId: traceId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			blocklistPattern: blocklistEntry.pattern,
			blocklistReason: blocklistEntry.reason,
			action: "skipping_field_creation",
		});
		return blocklistEntry;
	}

	return null;
}

/**
 * Comprehensive conflict detection for field creation
 *
 * Performs all conflict detection checks in a single function call,
 * providing a comprehensive analysis of potential conflicts before
 * attempting field creation. This includes standard field conflicts,
 * existing custom field conflicts, and blocklist violations.
 *
 * @param sourceField - Source field to check for conflicts
 * @param targetPlatformFields - Existing fields on target platform
 * @param sourcePlatform - Source platform identifier
 * @param targetPlatform - Target platform identifier
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Comprehensive conflict detection result
 *
 * @example
 * ```typescript
 * const conflictResult = detectFieldConflicts(
 *   apField,
 *   ccCustomFields,
 *   "ap",
 *   "cc",
 *   "req-123"
 * );
 *
 * if (conflictResult.hasConflict) {
 *   switch (conflictResult.conflictType) {
 *     case "standard_field":
 *       // Handle standard field mapping
 *       break;
 *     case "existing_custom_field":
 *       // Handle existing field mapping
 *       break;
 *     case "blocklist":
 *       // Handle blocklist violation
 *       break;
 *   }
 * } else {
 *   // Safe to create field
 * }
 * ```
 *
 * @since 2.0.0
 */
export function detectFieldConflicts(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetPlatformFields: (APGetCustomFieldType | GetCCCustomField)[],
	sourcePlatform: Platform,
	targetPlatform: Platform,
	requestId?: string,
): FieldConflictResult {
	const traceId = requestId || getRequestId();
	
	// Check for standard field conflicts
	const standardMapping = checkForStandardFieldMapping(
		sourceField.name,
		sourcePlatform,
		targetPlatform,
		traceId,
	);

	if (standardMapping) {
		return {
			hasConflict: true,
			conflictType: "standard_field",
			standardMapping,
		};
	}

	// Check for existing custom field conflicts
	const existingField = checkForExistingCustomFieldConflict(
		sourceField,
		targetPlatformFields,
		targetPlatform,
		traceId,
	);

	if (existingField) {
		return {
			hasConflict: true,
			conflictType: "existing_custom_field",
			existingField,
		};
	}

	// Check for blocklist violations (AP to CC only)
	if (
		sourcePlatform === "ap" &&
		targetPlatform === "cc" &&
		"dataType" in sourceField
	) {
		const blocklistEntry = checkApFieldCreationBlocklist(
			sourceField as APGetCustomFieldType,
			traceId,
		);

		if (blocklistEntry) {
			return {
				hasConflict: true,
				conflictType: "blocklist",
				blocklistInfo: blocklistEntry,
			};
		}
	}

	// No conflicts detected
	return {
		hasConflict: false,
	};
}
