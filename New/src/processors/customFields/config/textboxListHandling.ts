/**
 * TEXTBOX_LIST Field Handling Utilities
 *
 * Provides specialized utilities for handling TEXTBOX_LIST fields in the
 * AutoPatient system. TEXTBOX_LIST fields are multi-value text fields that
 * can store multiple text entries and need special handling for synchronization
 * with CliniCore multi-value fields.
 *
 * @fileoverview TEXTBOX_LIST field handling utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logCustomField, logDebug, logWarn } from "@/utils/logger";
import { TEXTBOX_LIST_COMPATIBLE_CC_TYPES } from "./fieldTypeMappings";

/**
 * Check if AP field is a TEXTBOX_LIST field
 *
 * @param apField - AutoPatient field to check
 * @returns True if field is TEXTBOX_LIST type
 */
export function isTextboxListField(apField: APGetCustomFieldType): boolean {
	return apField.dataType === "TEXTBOX_LIST";
}

/**
 * Check if CC field is compatible with TEXTBOX_LIST
 *
 * Determines if a CliniCore field can be mapped to an AutoPatient TEXTBOX_LIST
 * field based on its type and allowMultipleValues setting.
 *
 * @param ccField - CliniCore field to check
 * @returns True if field is compatible with TEXTBOX_LIST
 *
 * @example
 * ```typescript
 * const ccTextField = { type: "text", allowMultipleValues: true };
 * isTextboxListCompatible(ccTextField); // true
 *
 * const ccSelectField = { type: "select", allowMultipleValues: true };
 * isTextboxListCompatible(ccSelectField); // false
 * ```
 */
export function isTextboxListCompatible(ccField: GetCCCustomField): boolean {
	return (
		ccField.allowMultipleValues === true &&
		TEXTBOX_LIST_COMPATIBLE_CC_TYPES.includes(ccField.type)
	);
}

/**
 * Find matching TEXTBOX_LIST field for CC multi-value field
 *
 * Searches through AP fields to find a TEXTBOX_LIST field that could
 * be mapped to the given CC multi-value field.
 *
 * @param ccField - CC field to find match for
 * @param apFields - Array of AP fields to search
 * @param fieldMatcher - Function to check if fields match by name
 * @returns Matching AP TEXTBOX_LIST field or null
 */
export function findMatchingTextboxListField(
	ccField: GetCCCustomField,
	apFields: APGetCustomFieldType[],
	fieldMatcher: (apField: APGetCustomFieldType, ccField: GetCCCustomField) => boolean
): APGetCustomFieldType | null {
	if (!isTextboxListCompatible(ccField)) {
		return null;
	}

	for (const apField of apFields) {
		if (isTextboxListField(apField) && fieldMatcher(apField, ccField)) {
			logDebug("Found matching TEXTBOX_LIST field", {
				ccFieldName: ccField.name,
				ccFieldType: ccField.type,
				apFieldName: apField.name,
				apFieldType: apField.dataType,
			});
			return apField;
		}
	}

	return null;
}

/**
 * Convert CC multi-value field value to TEXTBOX_LIST format
 *
 * Transforms values from CC multi-value fields into the format expected
 * by AP TEXTBOX_LIST fields using option IDs as keys.
 *
 * @param value - CC field value (can be array or single value)
 * @param ccField - Source CC field
 * @param requestId - Request ID for logging
 * @returns Array of string values to be mapped to option IDs
 *
 * @example
 * ```typescript
 * // Array input (multi-value CC field)
 * convertToTextboxListValue(["item1", "item2"], ccField, "req-123");
 * // Result: ["item1", "item2"]
 *
 * // Single value input
 * convertToTextboxListValue("single item", ccField, "req-123");
 * // Result: ["single item"]
 * ```
 */
export function convertToTextboxListValue(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	requestId: string
): string[] {
	if (value === null || value === undefined) {
		logDebug("CC→AP TEXTBOX_LIST: null/undefined value", {
			requestId,
			ccFieldName: ccField.name,
		});
		return [];
	}

	if (Array.isArray(value)) {
		const stringValues = value
			.map(v => String(v).trim())
			.filter(v => v.length > 0);

		logDebug("CC→AP TEXTBOX_LIST: array conversion", {
			requestId,
			ccFieldName: ccField.name,
			originalCount: value.length,
			filteredCount: stringValues.length,
			result: stringValues,
		});
		return stringValues;
	}

	// Single values are converted to single-item arrays for consistent handling
	const result = String(value).trim();
	logDebug("CC→AP TEXTBOX_LIST: single value conversion", {
		requestId,
		ccFieldName: ccField.name,
		originalValue: value,
		result: [result],
	});
	return result ? [result] : [];
}

/**
 * Convert TEXTBOX_LIST value to CC multi-value format
 *
 * Transforms values from AP TEXTBOX_LIST fields into the format expected
 * by CC multi-value fields (typically arrays).
 *
 * @param value - TEXTBOX_LIST field value (comma-separated string)
 * @param apField - Source AP field
 * @param ccField - Target CC field
 * @param requestId - Request ID for logging
 * @returns Converted value for CC multi-value field
 *
 * @example
 * ```typescript
 * // Comma-separated input
 * convertFromTextboxListValue("item1,item2,item3", apField, ccField, "req-123");
 * // Result: ["item1", "item2", "item3"]
 *
 * // Single value input
 * convertFromTextboxListValue("single item", apField, ccField, "req-123");
 * // Result: ["single item"]
 * ```
 */
export function convertFromTextboxListValue(
	value: string | number | boolean | string[] | Record<string, string> | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): string[] | string {
	if (value === null || value === undefined) {
		logDebug("AP→CC TEXTBOX_LIST: null/undefined value", {
			requestId,
			apFieldName: apField.name,
		});
		return ccField.allowMultipleValues ? [] : "";
	}

	const stringValue = String(value).trim();
	if (!stringValue) {
		logDebug("AP→CC TEXTBOX_LIST: empty value", {
			requestId,
			apFieldName: apField.name,
		});
		return ccField.allowMultipleValues ? [] : "";
	}

	// Split by comma and clean up values
	const values = stringValue
		.split(",")
		.map(v => v.trim())
		.filter(v => v.length > 0);

	// Validate against CC field's allowedValues if they exist
	let validatedValues = values;
	if (ccField.allowedValues && ccField.allowedValues.length > 0) {
		const allowedValueStrings = ccField.allowedValues.map(av => av.value);
		const validValues: string[] = [];
		const invalidValues: string[] = [];

		values.forEach(value => {
			if (allowedValueStrings.includes(value)) {
				validValues.push(value);
			} else {
				invalidValues.push(value);
			}
		});

		if (invalidValues.length > 0) {
			logWarn("AP→CC TEXTBOX_LIST: some values rejected due to validation", {
				requestId,
				apFieldName: apField.name,
				ccFieldName: ccField.name,
				originalValues: values,
				validValues,
				invalidValues,
				allowedValues: allowedValueStrings.slice(0, 10), // Show first 10 for brevity
			});
		}

		validatedValues = validValues;
	}

	if (ccField.allowMultipleValues) {
		logDebug("AP→CC TEXTBOX_LIST: array conversion", {
			requestId,
			apFieldName: apField.name,
			originalValue: stringValue,
			originalCount: values.length,
			validatedCount: validatedValues.length,
		});
		return validatedValues;
	} else {
		// For single-value CC fields, return first value or join all
		const result = validatedValues.length > 0 ? validatedValues[0] : "";
		logDebug("AP→CC TEXTBOX_LIST: single value conversion", {
			requestId,
			apFieldName: apField.name,
			originalValue: stringValue,
			result,
			note: validatedValues.length > 1 ? "Multiple values truncated to first" : "Single value",
		});
		return result;
	}
}

/**
 * Validate TEXTBOX_LIST field configuration
 *
 * Checks if a TEXTBOX_LIST field is properly configured and can be
 * used for synchronization.
 *
 * @param apField - AP TEXTBOX_LIST field to validate
 * @returns Validation result with success status and any issues
 */
export function validateTextboxListField(apField: APGetCustomFieldType): {
	isValid: boolean;
	issues: string[];
} {
	const issues: string[] = [];

	if (!isTextboxListField(apField)) {
		issues.push("Field is not a TEXTBOX_LIST type");
	}

	if (!apField.name || apField.name.trim().length === 0) {
		issues.push("Field name is missing or empty");
	}

	if (!apField.id) {
		issues.push("Field ID is missing");
	}

	return {
		isValid: issues.length === 0,
		issues,
	};
}

/**
 * Generate field creation data for TEXTBOX_LIST field
 *
 * Creates the data structure needed to create a new TEXTBOX_LIST field
 * in AutoPatient based on a CC multi-value field.
 *
 * @param ccField - Source CC field
 * @param fieldName - Name for the new AP field
 * @param fieldKey - Field key for the new AP field
 * @returns Field creation data for AP API
 */
export function generateTextboxListFieldData(
	ccField: GetCCCustomField,
	fieldName: string,
	fieldKey: string
): {
	name: string;
	dataType: "TEXTBOX_LIST";
	placeholder: string;
	fieldKey: string;
	textBoxListOptions: { label: string; prefillValue: string; }[];
	acceptedFormat: string[];
} {
	logCustomField("Generating TEXTBOX_LIST field data", fieldName, {
		sourceType: ccField.type,
		sourceAllowMultiple: ccField.allowMultipleValues,
		targetType: "TEXTBOX_LIST",
	});

	// Determine acceptedFormat based on CC field type
	let acceptedFormat: string[];
	switch (ccField.type) {
		case "telephone":
			acceptedFormat = ["phone"];
			break;
		case "email":
			acceptedFormat = ["email"];
			break;
		case "number":
			acceptedFormat = ["number"];
			break;
		default:
			acceptedFormat = ["text"];
			break;
	}

	return {
		name: fieldName,
		dataType: "TEXTBOX_LIST",
		placeholder: `Multiple ${ccField.type} values (comma-separated)`,
		fieldKey,
		// AP API requires textBoxListOptions to be populated for TEXTBOX_LIST fields
		textBoxListOptions: [
			{
				label: "Value 1",
				prefillValue: ""
			}
		],
		// AP API requires acceptedFormat for TEXTBOX_LIST fields
		acceptedFormat
	};
}

/**
 * Check if field types are compatible for TEXTBOX_LIST mapping
 *
 * Determines if an AP TEXTBOX_LIST field and a CC field are compatible
 * for value synchronization.
 *
 * @param apField - AP TEXTBOX_LIST field
 * @param ccField - CC field to check compatibility with
 * @returns True if fields are compatible for mapping
 */
export function areTextboxListFieldsCompatible(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField
): boolean {
	if (!isTextboxListField(apField)) {
		return false;
	}

	return isTextboxListCompatible(ccField);
}
