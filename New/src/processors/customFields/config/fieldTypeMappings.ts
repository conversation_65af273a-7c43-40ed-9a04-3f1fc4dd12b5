/**
 * Field Type Mapping Configuration
 *
 * Centralized configuration for field type mappings between AutoPatient (AP)
 * and CliniCore (CC) platforms. Defines bidirectional type conversions with
 * special handling for multi-value fields, TEXTBOX_LIST, and FILE_UPLOAD.
 *
 * **AP to CC Mappings:**
 * - TEXT → text
 * - LARGE_TEXT → textarea
 * - NUMERICAL → number
 * - PHONE → telephone
 * - MONETORY → text
 * - CHECKBOX → select (allowMultipleValues: true)
 * - SINGLE_OPTIONS → select (allowMultipleValues: false)
 * - MULTIPLE_OPTIONS → select (allowMultipleValues: true)
 * - DATE → date
 * - RADIO → select (allowMultipleValues: false)
 * - RADIO (Yes/Ja, No/Nein values) → boolean
 * - EMAIL → email
 * - TEXTBOX_LIST → text (allowMultipleValues: true)
 * - FILE_UPLOAD → Skip entirely (do not sync)
 *
 * **CC to AP Mappings:**
 * - text → TEXT
 * - textarea → LARGE_TEXT
 * - select (allowMultipleValues: true) → MULTIPLE_OPTIONS
 * - select (allowMultipleValues: false) → SINGLE_OPTIONS
 * - boolean → RADIO (with Yes/Ja, No/Nein options)
 * - select-or-custom → SINGLE_OPTIONS
 * - text (allowMultipleValues: true) → TEXTBOX_LIST
 * - number → NUMERICAL
 * - number (allowMultipleValues: true) → TEXTBOX_LIST
 * - textarea (allowMultipleValues: true) → TEXTBOX_LIST
 * - telephone → PHONE
 * - telephone (allowMultipleValues: true) → TEXTBOX_LIST
 * - email → EMAIL
 * - email (allowMultipleValues: true) → TEXTBOX_LIST
 * - date → DATE
 * - Any unmapped CC field types → TEXT (fallback)
 *
 * **Special Rules:**
 * - Only create custom fields in AP, never create new fields in CC
 * - While syncing AP to CC, if any field is missing in CC, skip that field without noise
 * - Field matching uses intelligent normalization (German umlauts, spaces, etc.)
 * - If names match but types are incompatible, create new AP field with modified fieldKey
 *
 * @fileoverview Field type mapping configuration for custom field synchronization
 * @version 2.0.0
 * @since 2024-07-28
 */

/**
 * AutoPatient field types that can be mapped to CliniCore
 */
export type APFieldType = 
	| "TEXT" 
	| "LARGE_TEXT" 
	| "NUMERICAL" 
	| "PHONE" 
	| "MONETORY" 
	| "CHECKBOX" 
	| "SINGLE_OPTIONS" 
	| "MULTIPLE_OPTIONS" 
	| "DATE" 
	| "RADIO" 
	| "EMAIL" 
	| "TEXTBOX_LIST" 
	| "FILE_UPLOAD";

/**
 * CliniCore field types that can be mapped to AutoPatient
 */
export type CCFieldType =
	| "text"
	| "textarea"
	| "number"
	| "telephone"
	| "email"
	| "date"
	| "select"
	| "select-or-custom"
	| "boolean"
	| "medication"
	| "permanent-diagnoses";

/**
 * Field mapping configuration interface
 */
export interface FieldTypeMapping {
	/** Source field type */
	sourceType: APFieldType | CCFieldType;
	/** Target field type */
	targetType: CCFieldType | APFieldType;
	/** Whether target field allows multiple values (CC only) */
	allowMultipleValues?: boolean;
	/** Whether this mapping should be skipped entirely */
	skip?: boolean;
	/** Additional mapping notes */
	notes?: string;
}

/**
 * AutoPatient to CliniCore field type mappings
 * 
 * Maps AP field types to their corresponding CC field types with
 * special handling for multi-value fields and skip conditions.
 */
export const AP_TO_CC_MAPPINGS: FieldTypeMapping[] = [
	{
		sourceType: "TEXT",
		targetType: "text",
		notes: "Basic text field mapping"
	},
	{
		sourceType: "LARGE_TEXT",
		targetType: "textarea",
		notes: "Large text to textarea mapping"
	},
	{
		sourceType: "NUMERICAL",
		targetType: "number",
		notes: "Numerical to number mapping"
	},
	{
		sourceType: "PHONE",
		targetType: "telephone",
		notes: "Phone to telephone mapping"
	},
	{
		sourceType: "MONETORY",
		targetType: "text",
		notes: "Monetary values stored as text in CC"
	},
	{
		sourceType: "CHECKBOX",
		targetType: "select",
		allowMultipleValues: true,
		notes: "Checkbox to multi-select mapping"
	},
	{
		sourceType: "SINGLE_OPTIONS",
		targetType: "select",
		allowMultipleValues: false,
		notes: "Single options to single-select mapping"
	},
	{
		sourceType: "MULTIPLE_OPTIONS",
		targetType: "select",
		allowMultipleValues: true,
		notes: "Multiple options to multi-select mapping"
	},
	{
		sourceType: "DATE",
		targetType: "date",
		notes: "Date field mapping"
	},
	{
		sourceType: "RADIO",
		targetType: "select",
		allowMultipleValues: false,
		notes: "Radio to single-select mapping (or boolean for Yes/Ja,No/Nein)"
	},
	{
		sourceType: "EMAIL",
		targetType: "email",
		notes: "Email field mapping"
	},
	{
		sourceType: "TEXTBOX_LIST",
		targetType: "text",
		allowMultipleValues: true,
		notes: "Textbox list to multi-value text mapping"
	},
	{
		sourceType: "FILE_UPLOAD",
		targetType: "text", // Placeholder, will be skipped
		skip: true,
		notes: "File upload fields are skipped entirely"
	}
];

/**
 * CliniCore to AutoPatient field type mappings
 * 
 * Maps CC field types to their corresponding AP field types with
 * special handling for allowMultipleValues and TEXTBOX_LIST conversions.
 */
export const CC_TO_AP_MAPPINGS: FieldTypeMapping[] = [
	{
		sourceType: "text",
		targetType: "TEXT",
		notes: "Basic text field mapping"
	},
	{
		sourceType: "text",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		notes: "Multi-value text to TEXTBOX_LIST mapping"
	},
	{
		sourceType: "textarea",
		targetType: "LARGE_TEXT",
		notes: "Textarea to large text mapping"
	},
	{
		sourceType: "textarea",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		notes: "Multi-value textarea to TEXTBOX_LIST mapping"
	},
	{
		sourceType: "select",
		targetType: "MULTIPLE_OPTIONS",
		allowMultipleValues: true,
		notes: "Multi-select to multiple options mapping"
	},
	{
		sourceType: "select",
		targetType: "SINGLE_OPTIONS",
		allowMultipleValues: false,
		notes: "Single-select to single options mapping"
	},
	{
		sourceType: "boolean",
		targetType: "RADIO",
		notes: "Boolean to radio with Yes/Ja, No/Nein options"
	},
	{
		sourceType: "select-or-custom",
		targetType: "SINGLE_OPTIONS",
		notes: "Select-or-custom to single options mapping"
	},
	{
		sourceType: "number",
		targetType: "NUMERICAL",
		notes: "Number to numerical mapping"
	},
	{
		sourceType: "number",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		notes: "Multi-value number to TEXTBOX_LIST mapping"
	},
	{
		sourceType: "telephone",
		targetType: "PHONE",
		notes: "Telephone to phone mapping"
	},
	{
		sourceType: "telephone",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		notes: "Multi-value telephone to TEXTBOX_LIST mapping"
	},
	{
		sourceType: "email",
		targetType: "EMAIL",
		notes: "Email field mapping"
	},
	{
		sourceType: "email",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		notes: "Multi-value email to TEXTBOX_LIST mapping"
	},
	{
		sourceType: "date",
		targetType: "DATE",
		notes: "Date field mapping"
	},
	{
		sourceType: "medication",
		targetType: "TEXT",
		notes: "Medication field mapped to TEXT (fallback mapping)"
	},
	{
		sourceType: "permanent-diagnoses",
		targetType: "TEXT",
		notes: "Permanent diagnoses field mapped to TEXT (fallback mapping)"
	}
];

/**
 * Special mapping rules for TEXTBOX_LIST fields
 */
export const TEXTBOX_LIST_COMPATIBLE_CC_TYPES = [
	"text",
	"number", 
	"textarea",
	"telephone",
	"email"
];

/**
 * Boolean conversion options for RADIO fields
 */
export const BOOLEAN_RADIO_OPTIONS = {
	/** English options */
	ENGLISH: ["Yes", "No"],
	/** German options */
	GERMAN: ["Ja", "Nein"],
	/** Mixed options (English/German) */
	MIXED: ["Yes", "Ja", "No", "Nein"]
};

/**
 * Get AP to CC field type mapping
 * 
 * @param apType - AutoPatient field type
 * @param allowMultipleValues - Whether CC field should allow multiple values
 * @returns Field type mapping or null if not found
 */
export function getApToCcMapping(
	apType: APFieldType, 
	allowMultipleValues?: boolean
): FieldTypeMapping | null {
	return AP_TO_CC_MAPPINGS.find(mapping => 
		mapping.sourceType === apType && 
		(allowMultipleValues === undefined || mapping.allowMultipleValues === allowMultipleValues)
	) || null;
}

/**
 * Get CC to AP field type mapping
 *
 * @param ccType - CliniCore field type
 * @param allowMultipleValues - Whether CC field allows multiple values
 * @returns Field type mapping (uses fallback to TEXT if no direct mapping found)
 */
export function getCcToApMapping(
	ccType: CCFieldType,
	allowMultipleValues?: boolean
): FieldTypeMapping {
	// For multi-value fields, prioritize TEXTBOX_LIST mappings
	if (allowMultipleValues && TEXTBOX_LIST_COMPATIBLE_CC_TYPES.includes(ccType)) {
		const textboxMapping = CC_TO_AP_MAPPINGS.find(mapping =>
			mapping.sourceType === ccType &&
			mapping.targetType === "TEXTBOX_LIST" &&
			mapping.allowMultipleValues === true
		);
		if (textboxMapping) return textboxMapping;
	}

	// Find standard mapping
	const standardMapping = CC_TO_AP_MAPPINGS.find(mapping =>
		mapping.sourceType === ccType &&
		(allowMultipleValues === undefined || mapping.allowMultipleValues === allowMultipleValues)
	);

	// Return standard mapping or fallback to TEXT
	return standardMapping || getCcToApFallbackMapping(ccType);
}

/**
 * Check if AP field type should be skipped
 * 
 * @param apType - AutoPatient field type
 * @returns True if field should be skipped
 */
export function shouldSkipApField(apType: APFieldType): boolean {
	const mapping = AP_TO_CC_MAPPINGS.find(m => m.sourceType === apType);
	return mapping?.skip === true;
}

/**
 * Get fallback mapping for unmapped CC field types
 *
 * @param ccType - CliniCore field type that doesn't have a direct mapping
 * @returns Fallback field type mapping to TEXT
 */
export function getCcToApFallbackMapping(ccType: string): FieldTypeMapping {
	return {
		sourceType: ccType as CCFieldType,
		targetType: "TEXT",
		notes: `Fallback mapping for unmapped CC field type '${ccType}' → TEXT`
	};
}

/**
 * Check if RADIO field should be converted to boolean
 *
 * @param options - Radio field options
 * @returns True if should convert to boolean
 */
export function shouldConvertRadioToBoolean(options: string[]): boolean {
	if (options.length !== 2) return false;

	const normalizedOptions = options.map(opt => opt.toLowerCase().trim());

	// Check for Yes/No pattern
	if (normalizedOptions.includes("yes") && normalizedOptions.includes("no")) return true;

	// Check for Ja/Nein pattern
	if (normalizedOptions.includes("ja") && normalizedOptions.includes("nein")) return true;

	// Check for True/False pattern
	if (normalizedOptions.includes("true") && normalizedOptions.includes("false")) return true;

	return false;
}
