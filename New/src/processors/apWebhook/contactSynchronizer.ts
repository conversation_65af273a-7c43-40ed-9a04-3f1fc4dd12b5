/**
 * AutoPatient Contact Synchronization
 *
 * Provides contact synchronization operations for AutoPatient webhook events.
 * Follows the exact same pattern as CliniCore webhook handler but in reverse
 * direction (AP → CC instead of CC → AP).
 *
 * @fileoverview Contact synchronization utilities for AP webhook processing
 * @version 1.0.0
 * @since 2024-07-27
 */

import { eq, or } from "drizzle-orm";
import type { GetAPContactType, GetCCPatientType, PostCCPatientType } from "@type";
import { patientReq } from "@/apiClient";
import { getDb } from "@database";
import { dbSchema } from "@database";
import { logDatabaseError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import type {
	PatientSelect,
} from "./types";

/**
 * CliniCore patient upsert response
 */
interface CCPatientUpsertResponse {
	success: boolean;
	patient?: GetCCPatientType;
	error?: string;
}

/**
 * Sync buffer check result (following CC pattern)
 */
interface SyncBufferCheckResult {
	shouldProcess: boolean;
	reason: string;
	bufferThreshold: number;
}

/**
 * Patient lookup result (following CC pattern)
 */
interface PatientLookupResult {
	found: boolean;
	patient?: PatientSelect;
	lookupMethod?: "apId" | "email" | "phone" | "email_and_phone";
}

/**
 * Look up existing patient record in the database
 *
 * Searches for an existing patient record using multiple strategies,
 * following the exact same pattern as CliniCore webhook handler:
 * 1. Primary lookup by AutoPatient contact ID (apId)
 * 2. Secondary lookup by email and phone for potential matches
 *
 * @param apContactId - AutoPatient contact ID
 * @param email - Patient email address
 * @param phone - Patient phone number
 * @param requestId - Request ID for logging and tracing
 * @returns Patient lookup result with found record and lookup method
 */
export async function lookupExistingPatient(
	apContactId: string,
	email?: string,
	phone?: string,
	requestId?: string,
): Promise<PatientLookupResult> {
	const db = getDb();

	try {
		logDebug(`Looking up patient with AP ID: ${apContactId}`);

		// Primary lookup by apId
		const patientByApId = await db
			.select()
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.apId, apContactId))
			.limit(1);

		if (patientByApId.length > 0) {
			logDebug(`Found patient by AP ID: ${apContactId}`);
			return {
				found: true,
				patient: patientByApId[0],
				lookupMethod: "apId",
			};
		}

		// Secondary lookup by email if provided
		if (email) {
			logDebug(`Looking up patient by email: ${email}`);
			const patientByEmail = await db
				.select()
				.from(dbSchema.patient)
				.where(eq(dbSchema.patient.email, email))
				.limit(1);

			if (patientByEmail.length > 0) {
				logDebug(`Found patient by email: ${email}`);
				return {
					found: true,
					patient: patientByEmail[0],
					lookupMethod: "email",
				};
			}
		}

		// Tertiary lookup by phone if provided
		if (phone) {
			logDebug(`Looking up patient by phone: ${phone}`);
			const patientByPhone = await db
				.select()
				.from(dbSchema.patient)
				.where(eq(dbSchema.patient.phone, phone))
				.limit(1);

			if (patientByPhone.length > 0) {
				logDebug(`Found patient by phone: ${phone}`);
				return {
					found: true,
					patient: patientByPhone[0],
					lookupMethod: "phone",
				};
			}
		}

		logDebug(`No existing patient found for AP ID: ${apContactId}`);
		return {
			found: false,
		};
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"patient_lookup",
		);
		logError(`Database error during patient lookup: ${error}`);
		return {
			found: false,
		};
	}
}

/**
 * Check if event should be processed based on sync buffer timing
 *
 * Compares the webhook updatedAt timestamp with the database apUpdatedAt
 * timestamp to determine if the event is within the sync buffer window.
 * This prevents sync loops and unnecessary processing.
 * Follows the exact same pattern as CliniCore webhook handler.
 *
 * @param webhookUpdatedAt - Updated timestamp from AP API response
 * @param dbApUpdatedAt - Last AP updated timestamp from database
 * @param bufferTimeSec - Sync buffer time in seconds
 * @param requestId - Request ID for logging
 * @returns Sync buffer check result with processing decision
 */
export function checkSyncBuffer(
	webhookUpdatedAt: string,
	dbApUpdatedAt: Date | null,
	bufferTimeSec: number,
	requestId?: string,
): SyncBufferCheckResult {
	const webhookTime = new Date(webhookUpdatedAt);

	if (!dbApUpdatedAt) {
		logDebug("No previous AP timestamp, processing event");
		return {
			shouldProcess: true,
			reason: "No previous AP timestamp found",
			bufferThreshold: bufferTimeSec,
		};
	}

	const timeDifference =
		Math.abs(webhookTime.getTime() - dbApUpdatedAt.getTime()) / 1000;
	const bufferThreshold = bufferTimeSec;

	if (timeDifference <= bufferThreshold) {
		logInfo(
			`Event within sync buffer: ${timeDifference.toFixed(1)}s <= ${bufferThreshold}s`,
			{
				requestId,
				webhookTimestamp: webhookTime.toISOString(),
				databaseTimestamp: dbApUpdatedAt.toISOString(),
				timeDifferenceSeconds: timeDifference,
				bufferThresholdSeconds: bufferThreshold,
			}
		);
		return {
			shouldProcess: false,
			reason: `Event within sync buffer (${timeDifference.toFixed(1)}s <= ${bufferThreshold}s)`,
			bufferThreshold,
		};
	}

	logDebug(
		`Event outside sync buffer: ${timeDifference.toFixed(1)}s > ${bufferThreshold}s`,
		{
			requestId,
			webhookTimestamp: webhookTime.toISOString(),
			databaseTimestamp: dbApUpdatedAt.toISOString(),
			timeDifferenceSeconds: timeDifference,
			bufferThresholdSeconds: bufferThreshold,
		}
	);
	return {
		shouldProcess: true,
		reason: "Event outside sync buffer",
		bufferThreshold,
	};
}

/**
 * Upsert patient in CliniCore
 *
 * Creates or updates a patient record in CliniCore using the mapped data.
 * Follows the exact same pattern as CliniCore webhook handler but in reverse:
 * 1. If local database has ccId: Update the existing CC patient
 * 2. If no ccId in local database: Search for existing CC patient by email/phone
 * 3. If CC patient found: Update the existing patient
 * 4. If CC patient not found: Create new patient in CC
 *
 * @param ccPatientData - Mapped CliniCore patient data
 * @param existingCcId - Existing CliniCore patient ID from local database
 * @param email - Patient email for CC search
 * @param phone - Patient phone for CC search
 * @param requestId - Request ID for logging
 * @returns CliniCore patient upsert response
 */
export async function upsertCcPatient(
	ccPatientData: PostCCPatientType,
	existingCcId?: number,
	email?: string,
	phone?: string,
	requestId?: string,
): Promise<CCPatientUpsertResponse> {
	try {
		let ccPatient: GetCCPatientType;

		// If local database has ccId, update the existing CC patient
		if (existingCcId) {
			logInfo(`Updating existing CC patient ID: ${existingCcId}`);
			ccPatient = await patientReq.update(existingCcId, ccPatientData);
			logInfo(`Successfully updated CC patient ID: ${existingCcId}`);
		} else {
			// Search for existing CC patient using CC API by email and phone
			let foundCcPatient: GetCCPatientType | null = null;

			if (email) {
				logDebug(`Searching for CC patient by email: ${email}`);
				foundCcPatient = await patientReq.search(email);
			}

			if (!foundCcPatient && phone) {
				logDebug(`Searching for CC patient by phone: ${phone}`);
				foundCcPatient = await patientReq.search(phone);
			}

			if (foundCcPatient) {
				// Update existing CC patient
				logInfo(`Found existing CC patient ID: ${foundCcPatient.id}, updating`);
				ccPatient = await patientReq.update(foundCcPatient.id, ccPatientData);
				logInfo(`Successfully updated existing CC patient ID: ${foundCcPatient.id}`);
			} else {
				// Create new CC patient
				logInfo("No existing CC patient found, creating new patient");
				ccPatient = await patientReq.create(ccPatientData);
				logInfo(`Successfully created new CC patient ID: ${ccPatient.id}`);
			}
		}

		return {
			success: true,
			patient: ccPatient,
		};
	} catch (error) {
		logError(
			`Failed to ${existingCcId ? "update" : "upsert"} CC patient: ${error}`,
		);
		return {
			success: false,
			error: error instanceof Error ? error.message : String(error),
		};
	}
}

/**
 * Update patient record in database
 *
 * Updates the patient record with new data from both AutoPatient and CliniCore,
 * including sync timestamps and complete data preservation.
 * Uses full contact data from AP API instead of webhook payload.
 *
 * @param patientId - Database patient ID
 * @param apContact - Full AutoPatient contact data from API
 * @param ccPatient - CliniCore patient data
 * @param requestId - Request ID for logging
 * @returns Updated patient record or null if failed
 */
export async function updatePatientRecord(
	patientId: string,
	apContact: GetAPContactType,
	ccPatient?: GetCCPatientType,
	requestId?: string,
): Promise<PatientSelect | null> {
	const db = getDb();

	try {
		// Update patient data - only update timestamps when we make API calls
		const updateData: any = {
			apData: apContact,
			// apUpdatedAt: NOT SET - only set when we make AP API calls
		};

		if (ccPatient) {
			updateData.ccData = ccPatient;
			updateData.ccId = ccPatient.id;
			updateData.ccUpdatedAt = new Date(); // Set because we made CC API call
		}

		// Update email and phone for quick lookup
		if (apContact.email) {
			updateData.email = apContact.email;
		}
		if (apContact.phone) {
			updateData.phone = apContact.phone;
		}

		const updatedPatients = await db
			.update(dbSchema.patient)
			.set(updateData)
			.where(eq(dbSchema.patient.id, patientId))
			.returning();

		if (updatedPatients.length > 0) {
			logInfo(`Updated patient record: ${patientId}`);
			return updatedPatients[0];
		}

		logError(`Failed to update patient record: ${patientId}`);
		return null;
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"patient_update",
		);
		logError(`Database error updating patient: ${error}`);
		return null;
	}
}

/**
 * Create new patient record in database
 *
 * Creates a new patient record with data from both AutoPatient and CliniCore,
 * establishing the link between both platforms.
 * Uses full contact data from AP API instead of webhook payload.
 *
 * @param apContact - Full AutoPatient contact data from API
 * @param ccPatient - CliniCore patient data
 * @param requestId - Request ID for logging
 * @returns Created patient record or null if failed
 */
export async function createPatientRecord(
	apContact: GetAPContactType,
	ccPatient: GetCCPatientType,
	requestId?: string,
): Promise<PatientSelect | null> {
	const db = getDb();

	try {
		// Create patient record - only set ccUpdatedAt since we made CC API call
		// apUpdatedAt should NOT be set since we didn't make an AP API call
		const patientData: any = {
			apId: apContact.id,
			ccId: ccPatient.id,
			email: apContact.email || null,
			phone: apContact.phone || null,
			apData: apContact,
			ccData: ccPatient,
			// apUpdatedAt: NOT SET - only set when we make AP API calls
			ccUpdatedAt: new Date(), // Set because we made CC API call
		};

		const createdPatients = await db
			.insert(dbSchema.patient)
			.values(patientData)
			.returning();

		if (createdPatients.length > 0) {
			logInfo(
				`Created new patient record: ${createdPatients[0].id}`,
			);
			return createdPatients[0];
		}

		logError(
			`Failed to create patient record for AP ID: ${apContact.id}`,
		);
		return null;
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"patient_create",
		);
		logError(`Database error creating patient: ${error}`);
		return null;
	}
}
