/**
 * Patient Custom Field Value Converters
 *
 * Handles value conversion between AutoPatient and CliniCore custom field formats.
 * Provides intelligent type-aware conversion with proper error handling and validation.
 *
 * @fileoverview Value conversion utilities for patient custom field synchronization
 * @version 1.0.0
 * @since 2024-07-29
 */

import { logWarn } from "@/utils/logger";
import type {
	APCustomFieldValue,
	CCCustomFieldValue,
	ValueConversionResult,
	FieldMapping,
} from "./types";
import type { GetCCCustomField } from "@/type";
import { convertFromTextboxListValue } from "@/processors/customFields/config/textboxListHandling";


/**
 * Convert AutoPatient custom field value to CliniCore format
 *
 * @param apValue - AP custom field value
 * @param mapping - Field mapping from database
 * @param requestId - Request ID for logging
 * @returns Converted CC custom field value
 */
export function convertApValueToCc(
	apValue: APCustomFieldValue,
	mapping: FieldMapping,
	requestId: string,
): ValueConversionResult {
	try {
		const { apConfig, ccConfig } = mapping;
		const sourceValue = apValue.value;



		// Handle null/undefined values
		if (sourceValue === null || sourceValue === undefined || sourceValue === "") {
			return {
				success: true,
				convertedValue: null,
				originalValue: sourceValue,
			};
		}

		const ccValue: CCCustomFieldValue = {
			field: ccConfig,
			values: [],
			patient: null,
		};

		// Special handling for AP TEXTBOX_LIST → CC multi-value fields (all compatible types)
		const isTextboxListToMultiValue =
			apConfig.dataType === "TEXTBOX_LIST" &&
			ccConfig.allowMultipleValues &&
			["text", "textarea", "email", "telephone", "number"].includes(ccConfig.type);

		if (isTextboxListToMultiValue) {
			// Use the existing textboxListHandling function for consistent conversion
			const convertedValues = convertFromTextboxListValue(
				sourceValue,
				apConfig,
				ccConfig,
				requestId
			);

			// convertFromTextboxListValue returns string[] for multi-value fields
			if (Array.isArray(convertedValues)) {
				ccValue.values = convertedValues.map(value => ({ value }));
			} else {
				ccValue.values = [{ value: convertedValues }];
			}

			logWarn("Converting AP TEXTBOX_LIST to CC multi-value", {
				requestId,
				apFieldId: apValue.id,
				ccFieldId: ccConfig.id,
				ccFieldType: ccConfig.type,
				originalValue: sourceValue,
				convertedValues,
				valueCount: Array.isArray(convertedValues) ? convertedValues.length : 1,
			});

			return {
				success: true,
				convertedValue: ccValue,
				originalValue: sourceValue,
			};
		}

		// Convert based on CC field type for non-TEXTBOX_LIST cases
		switch (ccConfig.type) {
			case "text":
			case "textarea":
			case "email":
			case "telephone":
				ccValue.values = [{ value: String(sourceValue) }];
				break;

			case "number":
				const numValue = Number(sourceValue);
				if (isNaN(numValue)) {
					return {
						success: false,
						convertedValue: null,
						originalValue: sourceValue,
						error: `Invalid number value: ${sourceValue}`,
					};
				}
				ccValue.values = [{ value: String(numValue) }];
				break;

			case "date":
				// Date fields don't support arrays, use first value if array
				const dateInput = Array.isArray(sourceValue) ? sourceValue[0] : sourceValue;
				const dateValue = convertToDateString(dateInput);
				if (!dateValue) {
					return {
						success: false,
						convertedValue: null,
						originalValue: sourceValue,
						error: `Invalid date value: ${sourceValue}`,
					};
				}
				ccValue.values = [{ value: dateValue }];
				break;

			case "boolean":
				// Boolean fields don't support arrays, use first value if array
				const boolInput = Array.isArray(sourceValue) ? sourceValue[0] : sourceValue;
				const boolValue = convertToBooleanString(boolInput);
				ccValue.values = [{ value: boolValue }];
				break;

			case "select":
			case "select-or-custom":
				// Select fields don't support arrays, use first value if array
				const selectInput = Array.isArray(sourceValue) ? sourceValue[0] : sourceValue;
				return convertApSelectValueToCc(selectInput, ccConfig, ccValue);

			default:
				logWarn("Unknown CC field type, using text conversion", {
					requestId,
					ccFieldType: ccConfig.type,
					ccFieldId: ccConfig.id,
				});
				ccValue.values = [{ value: String(sourceValue) }];
		}

		return {
			success: true,
			convertedValue: ccValue,
			originalValue: sourceValue,
		};
	} catch (error) {
		return {
			success: false,
			convertedValue: null,
			originalValue: apValue.value,
			error: `Conversion error: ${String(error)}`,
		};
	}
}

/**
 * Convert CliniCore custom field value to AutoPatient format
 *
 * @param ccValue - CC custom field value
 * @param mapping - Field mapping from database
 * @param requestId - Request ID for logging
 * @returns Converted AP custom field value
 */
export function convertCcValueToAp(
	ccValue: CCCustomFieldValue,
	mapping: FieldMapping,
	requestId: string,
): ValueConversionResult {
	try {
		const { apConfig, ccConfig } = mapping;
		const sourceValues = ccValue.values;

		// Handle empty values
		if (!sourceValues || sourceValues.length === 0) {
			return {
				success: true,
				convertedValue: null,
				originalValue: sourceValues,
			};
		}

		const apValue: APCustomFieldValue = {
			id: apConfig.id,
			value: null,
		};

		// Special handling for CC multi-value fields → AP TEXTBOX_LIST
		const isCcMultiValueToTextboxList =
			ccConfig.allowMultipleValues &&
			apConfig.dataType === "TEXTBOX_LIST" &&
			["text", "textarea", "email", "telephone", "number", "date"].includes(ccConfig.type);

		if (isCcMultiValueToTextboxList) {
			const values = sourceValues.map(v => v.value).filter(Boolean) as string[];

			logWarn("Converting CC multi-value to AP TEXTBOX_LIST - creating marker for async processing", {
				requestId,
				ccFieldId: ccValue.field.id,
				ccFieldName: ccConfig.name,
				ccFieldType: ccConfig.type,
				apFieldId: apConfig.id,
				apFieldName: apConfig.name,
				sourceValues: sourceValues.map(v => v.value),
				valueCount: values.length,
				marker: `__TEXTBOX_LIST_VALUES__:${JSON.stringify(values)}`,
				note: "This marker will be processed by textboxListProcessor.ts to create proper field_value format",
			});

			// Mark this field for special TEXTBOX_LIST processing
			// The actual field_value mapping will be done by the async processor
			apValue.value = `__TEXTBOX_LIST_VALUES__:${JSON.stringify(values)}`;

			return {
				success: true,
				convertedValue: apValue,
				originalValue: sourceValues,
			};
		}

		// Convert based on AP field type
		switch (apConfig.dataType) {
			case "TEXT":
			case "LARGE_TEXT":
			case "EMAIL":
			case "PHONE":
			case "MONETORY":
				apValue.value = sourceValues[0]?.value || "";
				break;

			case "NUMERICAL":
				const numStr = sourceValues[0]?.value || "0";
				const numValue = Number(numStr);
				if (isNaN(numValue)) {
					return {
						success: false,
						convertedValue: null,
						originalValue: sourceValues,
						error: `Invalid number value: ${numStr}`,
					};
				}
				apValue.value = numValue;
				break;

			case "DATE":
				const dateStr = sourceValues[0]?.value || "";
				apValue.value = dateStr;
				break;

			case "RADIO":
			case "SINGLE_OPTIONS":
				apValue.value = sourceValues[0]?.value || "";
				break;

			case "CHECKBOX":
			case "MULTIPLE_OPTIONS":
			case "TEXTBOX_LIST":
				// Handle multiple values
				const values = sourceValues.map(v => v.value).filter(Boolean);
				apValue.value = values.join(", ");

				// Add logging for TEXTBOX_LIST specifically to track conversion
				if (apConfig.dataType === "TEXTBOX_LIST") {
					logWarn("Converting CC to AP TEXTBOX_LIST", {
						requestId,
						ccFieldId: ccValue.field.id,
						apFieldId: apConfig.id,
						sourceValues: sourceValues.map(v => v.value),
						convertedValue: apValue.value,
						valueCount: values.length,
					});
				}
				break;

			default:
				logWarn("Unknown AP field type, using text conversion", {
					requestId,
					apFieldType: apConfig.dataType,
					apFieldId: apConfig.id,
				});
				apValue.value = sourceValues[0]?.value || "";
		}

		return {
			success: true,
			convertedValue: apValue,
			originalValue: sourceValues,
		};
	} catch (error) {
		return {
			success: false,
			convertedValue: null,
			originalValue: ccValue.values,
			error: `Conversion error: ${String(error)}`,
		};
	}
}

/**
 * Convert AP select value to CC format with allowed values handling
 */
function convertApSelectValueToCc(
	sourceValue: string | number | boolean | null,
	ccConfig: GetCCCustomField,
	ccValue: CCCustomFieldValue,
): ValueConversionResult {
	const valueStr = String(sourceValue);
	
	// Handle multiple values (comma-separated)
	const values = valueStr.split(",").map(v => v.trim()).filter(Boolean);
	
	for (const value of values) {
		// Try to find matching allowed value
		const allowedValue = ccConfig.allowedValues?.find(av => av.value === value);
		
		if (allowedValue) {
			// Use predefined option ID
			ccValue.values.push({ id: allowedValue.id });
		} else if (ccConfig.type === "select-or-custom") {
			// Allow custom value for select-or-custom fields
			ccValue.values.push({ value });
		} else {
			// For strict select fields, use the value as-is (may cause validation error)
			ccValue.values.push({ value });
		}
	}

	return {
		success: true,
		convertedValue: ccValue,
		originalValue: sourceValue,
		warnings: ccConfig.allowedValues?.length > 0 && !ccConfig.allowedValues.some(av => values.includes(av.value))
			? [`Value "${valueStr}" not found in allowed values for field "${ccConfig.name}"`]
			: undefined,
	};
}

/**
 * Convert value to date string format
 */
function convertToDateString(value: string | number | boolean | null): string | null {
	if (!value) return null;
	
	try {
		const date = new Date(String(value));
		if (isNaN(date.getTime())) return null;
		
		// Return ISO date string (YYYY-MM-DD)
		return date.toISOString().split('T')[0];
	} catch {
		return null;
	}
}

/**
 * Convert value to boolean string representation
 */
function convertToBooleanString(value: string | number | boolean | null): string {
	if (typeof value === "boolean") {
		return value ? "true" : "false";
	}
	
	const str = String(value).toLowerCase().trim();
	
	// Handle common boolean representations
	if (["true", "1", "yes", "ja", "on", "enabled"].includes(str)) {
		return "true";
	}
	
	if (["false", "0", "no", "nein", "off", "disabled"].includes(str)) {
		return "false";
	}
	
	// Default to false for unknown values
	return "false";
}
