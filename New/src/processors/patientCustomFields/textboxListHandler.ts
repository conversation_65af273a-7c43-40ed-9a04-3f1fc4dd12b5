import { logDebug, logError } from "@/utils/logger";
import type { APGetCustomFieldType } from "@/type";
import type { APCustomFieldValue } from "./types";
import { apCustomfield } from "@/apiClient";

/**
 * TEXTBOX_LIST Field Handler
 * 
 * Handles the complex logic for updating AP TEXTBOX_LIST fields which require:
 * 1. Fetching current field configuration to get option IDs
 * 2. Ensuring enough options exist for all values
 * 3. Mapping values to option IDs using value format
 *
 * TEXTBOX_LIST Update Format:
 * {
 *   "id": "field-id",
 *   "value": {
 *     "option-id-1": "value1",
 *     "option-id-2": "value2"
 *   }
 * }
 */

export interface TextboxListOption {
	id: string;
	label: string;
	prefillValue: string;
}

export interface TextboxListUpdateResult {
	success: boolean;
	fieldValue: Record<string, string>;
	optionsUsed: number;
	optionsCreated?: number;
	error?: string;
}

/**
 * Convert CC multi-value field values to AP TEXTBOX_LIST value format
 *
 * @param values - Array of string values from CC field
 * @param apField - AP TEXTBOX_LIST field configuration
 * @param requestId - Request ID for logging
 * @returns Result with value object or error
 */
export async function convertToTextboxListFieldValue(
	values: string[],
	apField: APGetCustomFieldType,
	requestId: string,
	currentContactData?: { customFields?: Array<{ id: string; value?: any }> }
): Promise<TextboxListUpdateResult> {
	try {

		// Get current field options
		const currentOptions = extractTextboxListOptions(apField);

		// Check if we need more options
		const optionsNeeded = values.length;
		const optionsAvailable = currentOptions.length;
		
		let finalOptions = currentOptions;
		let optionsCreated = 0;

		if (optionsNeeded > optionsAvailable) {
			// Update the field to add more options
			const updateResult = await updateTextboxListFieldOptions(
				apField,
				optionsNeeded,
				requestId
			);

			if (updateResult.success) {
				finalOptions = updateResult.updatedOptions;
				optionsCreated = updateResult.optionsCreated;
			} else {
				logError("Failed to add more TEXTBOX_LIST options", {
					requestId,
					apFieldId: apField.id,
					error: updateResult.error,
					note: "Using available options only",
				});
			}
		}

		// Efficiently map values to option IDs - only send what we need
		const fieldValue: Record<string, string> = {};

		// Get currently set option IDs that have values (to clear unused ones)
		const currentlySetOptionIds: string[] = [];
		if (currentContactData?.customFields) {
			const currentField = currentContactData.customFields.find(cf => cf.id === apField.id);
			if (currentField?.value && typeof currentField.value === 'object') {
				currentlySetOptionIds.push(...Object.keys(currentField.value).filter(id =>
					currentField.value[id] && currentField.value[id] !== ""
				));
			}
		}

		// Get the exact number of option IDs we need for our values
		const optionsToUse = Math.min(values.length, finalOptions.length);
		const optionIdsNeeded = finalOptions.slice(0, optionsToUse).map(opt => opt.id);
		const optionIdsNeededSet = new Set(optionIdsNeeded);

		// Clear only currently set option IDs that we're not going to use
		const optionIdsToClear = currentlySetOptionIds.filter(id => !optionIdsNeededSet.has(id));
		for (const optionId of optionIdsToClear) {
			fieldValue[optionId] = "";
		}

		// Set our new values to the exact option IDs we need
		for (let i = 0; i < optionsToUse; i++) {
			const optionId = finalOptions[i].id;
			const value = values[i];
			fieldValue[optionId] = value;
		}

		logDebug("Efficiently mapped TEXTBOX_LIST values", {
			requestId,
			apFieldId: apField.id,
			valuesNeeded: values.length,
			optionsUsed: optionsToUse,
			optionsCleared: optionIdsToClear.length,
			totalOptionsSent: Object.keys(fieldValue).length,
			clearedOptionIds: optionIdsToClear,
			usedOptionIds: optionIdsNeeded,
			note: "Only sending necessary option IDs to avoid empty boxes",
		});

		return {
			success: true,
			fieldValue,
			optionsUsed: optionsToUse,
			optionsCreated: optionsCreated > 0 ? optionsCreated : undefined,
		};

	} catch (error) {
		logError("Error converting to TEXTBOX_LIST value", {
			requestId,
			apFieldId: apField.id,
			error: String(error),
		});

		return {
			success: false,
			fieldValue: {},
			optionsUsed: 0,
			error: String(error),
		};
	}
}

/**
 * Extract TEXTBOX_LIST options from AP field configuration
 */
function extractTextboxListOptions(apField: APGetCustomFieldType): TextboxListOption[] {
	if (!apField.picklistOptions) {
		return [];
	}

	// Handle both string[] and object[] formats
	if (Array.isArray(apField.picklistOptions)) {
		return apField.picklistOptions.map((option, index) => {
			if (typeof option === 'string') {
				// Legacy string format - generate IDs
				return {
					id: `option-${index}`,
					label: option,
					prefillValue: option,
				};
			} else {
				// New object format with IDs
				return {
					id: option.id,
					label: option.label,
					prefillValue: option.prefillValue,
				};
			}
		});
	}

	return [];
}

/**
 * Create APCustomFieldValue for TEXTBOX_LIST field
 */
export function createTextboxListCustomFieldValue(
	fieldId: string,
	fieldValue: Record<string, string>
): APCustomFieldValue {
	return {
		id: fieldId,
		value: fieldValue,
	};
}

/**
 * Update AP TEXTBOX_LIST field to add more options
 */
async function updateTextboxListFieldOptions(
	apField: APGetCustomFieldType,
	optionsNeeded: number,
	requestId: string
): Promise<{
	success: boolean;
	updatedOptions: TextboxListOption[];
	optionsCreated: number;
	error?: string;
}> {
	try {
		const currentOptions = extractTextboxListOptions(apField);
		const optionsToCreate = optionsNeeded - currentOptions.length;

		// Generate new options
		const newOptions: { label: string; prefillValue: string }[] = [];
		for (let i = 0; i < optionsToCreate; i++) {
			const optionNumber = currentOptions.length + i + 1;
			newOptions.push({
				label: `Value ${optionNumber}`,
				prefillValue: "",
			});
		}

		// Prepare options for the AP API update call
		const allOptions = [
			...currentOptions.map(opt => ({ label: opt.label, prefillValue: opt.prefillValue })),
			...newOptions
		];

		// Make the actual AP API call to update the field
		const updatePayload = {
			name: apField.name,
			dataType: apField.dataType,
			textBoxListOptions: allOptions,
		};

		const updatedField = await apCustomfield.update(apField.id, updatePayload);

		// Extract the updated options with their new IDs
		const updatedOptions = extractTextboxListOptions(updatedField);

		return {
			success: true,
			updatedOptions,
			optionsCreated: optionsToCreate,
		};

	} catch (error) {
		logError("Error updating TEXTBOX_LIST field options", {
			requestId,
			apFieldId: apField.id,
			error: String(error),
		});

		return {
			success: false,
			updatedOptions: extractTextboxListOptions(apField),
			optionsCreated: 0,
			error: String(error),
		};
	}
}
