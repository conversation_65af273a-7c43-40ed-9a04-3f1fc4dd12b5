import { logDebug, logError } from "@/utils/logger";
import type { APGetCustomFieldType } from "@/type";
import type { APCustomFieldValue } from "./types";
import { convertToTextboxListFieldValue, createTextboxListCustomFieldValue } from "./textboxListHandler";

/**
 * TEXTBOX_LIST Field Processor
 * 
 * Processes AP custom field values that have been marked for TEXTBOX_LIST conversion.
 * Handles the async operations required for proper TEXTBOX_LIST field updates.
 */

export interface TextboxListProcessingResult {
	success: boolean;
	processedFields: APCustomFieldValue[];
	errors: string[];
	warnings: string[];
}

/**
 * Process custom field values and convert TEXTBOX_LIST fields to proper format
 * 
 * @param customFields - Array of AP custom field values
 * @param apFieldsMap - Map of AP field IDs to field configurations
 * @param requestId - Request ID for logging
 * @returns Processing result with converted fields
 */
export async function processTextboxListFields(
	customFields: APCustomFieldValue[],
	apFieldsMap: Map<string, APGetCustomFieldType>,
	requestId: string,
	currentContactData?: { customFields?: Array<{ id: string; value?: any }> }
): Promise<TextboxListProcessingResult> {
	const processedFields: APCustomFieldValue[] = [];
	const errors: string[] = [];
	const warnings: string[] = [];

	for (const field of customFields) {
		try {
			// Check if this field needs TEXTBOX_LIST processing
			if (typeof field.value === 'string' && field.value.startsWith('__TEXTBOX_LIST_VALUES__:')) {
				const result = await processTextboxListField(field, apFieldsMap, requestId, currentContactData);
				
				if (result.success) {
					processedFields.push(result.processedField);
					if (result.warnings) {
						warnings.push(...result.warnings);
					}
				} else {
					errors.push(`Field ${field.id}: ${result.error}`);
					// Add the field as-is if processing fails
					processedFields.push(field);
				}
			} else {
				// Regular field, no processing needed
				processedFields.push(field);
			}
		} catch (error) {
			logError("Error processing field", {
				requestId,
				fieldId: field.id,
				error: String(error),
			});
			errors.push(`Field ${field.id}: ${String(error)}`);
			// Add the field as-is if processing fails
			processedFields.push(field);
		}
	}

	return {
		success: errors.length === 0,
		processedFields,
		errors,
		warnings,
	};
}

/**
 * Process a single TEXTBOX_LIST field
 */
async function processTextboxListField(
	field: APCustomFieldValue,
	apFieldsMap: Map<string, APGetCustomFieldType>,
	requestId: string,
	currentContactData?: { customFields?: Array<{ id: string; value?: any }> }
): Promise<{
	success: boolean;
	processedField: APCustomFieldValue;
	warnings?: string[];
	error?: string;
}> {
	try {
		// Extract values from the marker
		const markerPrefix = '__TEXTBOX_LIST_VALUES__:';
		const valuesJson = (field.value as string).substring(markerPrefix.length);
		const values: string[] = JSON.parse(valuesJson);

		logDebug("Processing TEXTBOX_LIST field", {
			requestId,
			fieldId: field.id,
			valuesCount: values.length,
			values,
		});

		// Get the AP field configuration
		const apField = apFieldsMap.get(field.id);
		if (!apField) {
			return {
				success: false,
				processedField: field,
				error: `AP field configuration not found for field ID: ${field.id}`,
			};
		}

		if (apField.dataType !== 'TEXTBOX_LIST') {
			return {
				success: false,
				processedField: field,
				error: `Field ${field.id} is not a TEXTBOX_LIST field (type: ${apField.dataType})`,
			};
		}

		// Convert values to value format
		const conversionResult = await convertToTextboxListFieldValue(values, apField, requestId, currentContactData);
		
		if (!conversionResult.success) {
			return {
				success: false,
				processedField: field,
				error: conversionResult.error || 'Unknown conversion error',
			};
		}

		// Create the properly formatted field value
		const processedField = createTextboxListCustomFieldValue(field.id, conversionResult.fieldValue);

		const warnings: string[] = [];
		if (conversionResult.optionsUsed < values.length) {
			warnings.push(`Only ${conversionResult.optionsUsed} of ${values.length} values could be mapped due to insufficient options`);
		}

		logDebug("TEXTBOX_LIST field processed successfully", {
			requestId,
			fieldId: field.id,
			originalValues: values,
			fieldValue: conversionResult.fieldValue,
			optionsUsed: conversionResult.optionsUsed,
		});

		return {
			success: true,
			processedField,
			warnings: warnings.length > 0 ? warnings : undefined,
		};

	} catch (error) {
		logError("Error processing TEXTBOX_LIST field", {
			requestId,
			fieldId: field.id,
			error: String(error),
		});

		return {
			success: false,
			processedField: field,
			error: String(error),
		};
	}
}

/**
 * Check if custom fields contain any TEXTBOX_LIST markers
 */
export function hasTextboxListFields(customFields: APCustomFieldValue[]): boolean {
	return customFields.some(field => 
		typeof field.value === 'string' && field.value.startsWith('__TEXTBOX_LIST_VALUES__:')
	);
}

/**
 * Extract TEXTBOX_LIST field IDs that need processing
 */
export function getTextboxListFieldIds(customFields: APCustomFieldValue[]): string[] {
	return customFields
		.filter(field => 
			typeof field.value === 'string' && field.value.startsWith('__TEXTBOX_LIST_VALUES__:')
		)
		.map(field => field.id);
}
