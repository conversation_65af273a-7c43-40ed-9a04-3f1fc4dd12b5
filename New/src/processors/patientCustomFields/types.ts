/**
 * Patient Custom Field Value Synchronization Types
 *
 * Type definitions for patient custom field value synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Handles value conversion
 * and synchronization with comprehensive error handling and logging.
 *
 * @fileoverview Patient custom field value synchronization type definitions
 * @version 1.0.0
 * @since 2024-07-29
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@/type";

/**
 * Platform identifier for synchronization operations
 */
export type Platform = "ap" | "cc";

/**
 * AutoPatient custom field value structure
 */
export interface APCustomFieldValue {
	id: string;
	value?: string | number | boolean | null | Record<string, string>;
	field_value?: Record<string, string>;
}

/**
 * CliniCore custom field value structure
 */
export interface CCCustomFieldValue {
	field: GetCCCustomField;
	values: Array<{
		id?: number;
		value?: string;
	}>;
	patient: number | null;
}

/**
 * Field mapping from database
 */
export interface FieldMapping {
	apId: string;
	ccId: number;
	apConfig: APGetCustomFieldType;
	ccConfig: GetCCCustomField;
	mappingType: string;
}

/**
 * Patient data with custom field values
 */
export interface PatientData {
	id: string | number;
	customFields: APCustomFieldValue[] | CCCustomFieldValue[];
}

/**
 * Value conversion result
 */
export interface ValueConversionResult {
	success: boolean;
	convertedValue: APCustomFieldValue | CCCustomFieldValue | null;
	originalValue: string | number | boolean | string[] | Record<string, string> | null | undefined | Array<{ id?: number; value?: string }>;
	warnings?: string[];
	error?: string;
}

/**
 * Field value synchronization result
 */
export interface FieldValueSyncResult {
	fieldName: string;
	apFieldId?: string;
	ccFieldId?: number;
	success: boolean;
	action: "updated" | "skipped" | "failed";
	originalValue?: string | number | boolean | string[] | Record<string, string> | null | undefined | Array<{ id?: number; value?: string }>;
	convertedValue?: string | number | boolean | string[] | Record<string, string> | null | undefined | Array<{ id?: number; value?: string }>;
	error?: string;
	warnings?: string[];
}

/**
 * Patient custom field synchronization result
 */
export interface PatientCustomFieldSyncResult {
	success: boolean;
	patientId: string;
	targetPlatform: Platform;
	fieldsProcessed: number;
	fieldsUpdated: number;
	fieldsSkipped: number;
	fieldsFailed: number;
	results: FieldValueSyncResult[];
	errors: string[];
	warnings: string[];
	executionTimeMs: number;
}



/**
 * Patient lookup result
 */
export interface PatientLookupResult {
	found: boolean;
	apId?: string;
	ccId?: number;
	error?: string;
}

/**
 * Rate limit result
 */
export interface RateLimitResult {
	allowed: boolean;
	requestCount: number;
	maxRequests: number;
	remainingRequests: number;
	resetTime: Date;
	resetInSeconds: number;
	error?: string;
}
